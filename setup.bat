@echo off
echo 🚀 Setting up WhatsApp Clone...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ and try again.
    pause
    exit /b 1
)

echo ✅ Node.js detected

REM Install root dependencies
echo 📦 Installing root dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install root dependencies
    pause
    exit /b 1
)

REM Install backend dependencies
echo 📦 Installing backend dependencies...
cd backend
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

REM Install frontend dependencies
echo 📦 Installing frontend dependencies...
cd frontend
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

REM Copy environment files
echo 📝 Setting up environment files...

if not exist "backend\.env" (
    copy "backend\.env.example" "backend\.env" >nul
    echo ✅ Created backend\.env from example
    echo ⚠️  Please update backend\.env with your configuration
) else (
    echo ℹ️  backend\.env already exists
)

if not exist "frontend\.env" (
    copy "frontend\.env.example" "frontend\.env" >nul
    echo ✅ Created frontend\.env from example
    echo ⚠️  Please update frontend\.env with your configuration
) else (
    echo ℹ️  frontend\.env already exists
)

echo.
echo 🎉 Setup complete!
echo.
echo 📋 Next steps:
echo 1. Set up Firebase project and update environment variables
echo 2. Set up MongoDB (local or Atlas) and update connection string
echo 3. Update environment variables in backend\.env and frontend\.env
echo 4. Run 'npm run dev' to start both servers
echo.
echo 📚 For detailed instructions, see README.md
echo.
pause
