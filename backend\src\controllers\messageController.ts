import { Response } from 'express';
import mongoose from 'mongoose';
import { Message } from '../models/Message';
import { Chat } from '../models/Chat';
import { AuthenticatedRequest } from '../middlewares/auth';
import { ApiError } from '../middlewares/errorHandler';

/**
 * Message controller
 * Handles message-related operations
 */
export const messageController = {
  /**
   * Get messages for a chat (with cursor-based pagination)
   */
  getChatMessages: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { chatId } = req.params;
    const { limit = 50, cursor } = req.query;

    if (!mongoose.Types.ObjectId.isValid(chatId)) {
      throw new ApiError(400, 'Invalid chat ID');
    }

    // Verify user is member of the chat
    const chat = await Chat.findOne({
      _id: chatId,
      members: req.user._id,
      isActive: true,
    });

    if (!chat) {
      throw new ApiError(404, 'Chat not found or access denied');
    }

    const limitNum = parseInt(limit as string);
    const cursorId = cursor ? new mongoose.Types.ObjectId(cursor as string) : undefined;

    const messages = await Message.findChatMessages(
      new mongoose.Types.ObjectId(chatId),
      limitNum,
      cursorId
    );

    // Determine if there are more messages
    const hasMore = messages.length === limitNum;
    const nextCursor = messages.length > 0 ? messages[messages.length - 1]._id : null;

    res.status(200).json({
      messages: messages.reverse(), // Reverse to show oldest first
      hasMore,
      nextCursor,
      pagination: {
        limit: limitNum,
        cursor: cursor || null,
        nextCursor,
      },
    });
  },

  /**
   * Send a new message
   */
  sendMessage: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { chatId, text, replyTo } = req.body;

    // Validate input
    if (!chatId || !mongoose.Types.ObjectId.isValid(chatId)) {
      throw new ApiError(400, 'Valid chat ID is required');
    }

    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      throw new ApiError(400, 'Message text is required');
    }

    if (text.trim().length > 4000) {
      throw new ApiError(400, 'Message text is too long (max 4000 characters)');
    }

    // Verify user is member of the chat
    const chat = await Chat.findOne({
      _id: chatId,
      members: req.user._id,
      isActive: true,
    });

    if (!chat) {
      throw new ApiError(404, 'Chat not found or access denied');
    }

    // Validate replyTo message if provided
    let replyToMessage = null;
    if (replyTo) {
      if (!mongoose.Types.ObjectId.isValid(replyTo)) {
        throw new ApiError(400, 'Invalid reply message ID');
      }

      replyToMessage = await Message.findOne({
        _id: replyTo,
        chatId,
        isDeleted: false,
      });

      if (!replyToMessage) {
        throw new ApiError(404, 'Reply message not found');
      }
    }

    // Create message
    const message = new Message({
      chatId: new mongoose.Types.ObjectId(chatId),
      senderId: req.user._id,
      text: text.trim(),
      replyTo: replyToMessage ? replyToMessage._id : undefined,
    });

    await message.save();
    await message.populate('senderId', 'name email avatar');
    
    if (replyToMessage) {
      await message.populate('replyTo', 'text senderId timestamp');
    }

    // Update chat's last activity and last message
    chat.lastActivity = new Date();
    chat.lastMessage = message._id;
    await chat.save();

    res.status(201).json({
      message: 'Message sent successfully',
      data: message,
    });
  },

  /**
   * Edit a message
   */
  editMessage: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { messageId } = req.params;
    const { text } = req.body;

    if (!mongoose.Types.ObjectId.isValid(messageId)) {
      throw new ApiError(400, 'Invalid message ID');
    }

    if (!text || typeof text !== 'string' || text.trim().length === 0) {
      throw new ApiError(400, 'Message text is required');
    }

    if (text.trim().length > 4000) {
      throw new ApiError(400, 'Message text is too long (max 4000 characters)');
    }

    const message = await Message.findOne({
      _id: messageId,
      senderId: req.user._id,
      isDeleted: false,
    });

    if (!message) {
      throw new ApiError(404, 'Message not found or access denied');
    }

    // Check if message is too old to edit (15 minutes)
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    if (message.timestamp < fifteenMinutesAgo) {
      throw new ApiError(400, 'Message is too old to edit');
    }

    await message.editMessage(text.trim());
    await message.populate('senderId', 'name email avatar');
    await message.populate('replyTo', 'text senderId timestamp');

    res.status(200).json({
      message: 'Message updated successfully',
      data: message,
    });
  },

  /**
   * Delete a message
   */
  deleteMessage: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { messageId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(messageId)) {
      throw new ApiError(400, 'Invalid message ID');
    }

    const message = await Message.findOne({
      _id: messageId,
      senderId: req.user._id,
      isDeleted: false,
    });

    if (!message) {
      throw new ApiError(404, 'Message not found or access denied');
    }

    await message.deleteMessage();

    res.status(200).json({
      message: 'Message deleted successfully',
    });
  },

  /**
   * Mark message as delivered
   */
  markAsDelivered: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { messageId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(messageId)) {
      throw new ApiError(400, 'Invalid message ID');
    }

    const message = await Message.findById(messageId);

    if (!message || message.isDeleted) {
      throw new ApiError(404, 'Message not found');
    }

    // Don't mark own messages as delivered
    if (message.senderId.equals(req.user._id)) {
      res.status(200).json({ message: 'Cannot mark own message as delivered' });
      return;
    }

    // Verify user is member of the chat
    const chat = await Chat.findOne({
      _id: message.chatId,
      members: req.user._id,
      isActive: true,
    });

    if (!chat) {
      throw new ApiError(404, 'Chat not found or access denied');
    }

    await message.markAsDelivered(req.user._id);

    res.status(200).json({
      message: 'Message marked as delivered',
    });
  },

  /**
   * Mark message as seen
   */
  markAsSeen: async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user) {
      throw new ApiError(401, 'User not authenticated');
    }

    const { messageId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(messageId)) {
      throw new ApiError(400, 'Invalid message ID');
    }

    const message = await Message.findById(messageId);

    if (!message || message.isDeleted) {
      throw new ApiError(404, 'Message not found');
    }

    // Don't mark own messages as seen
    if (message.senderId.equals(req.user._id)) {
      res.status(200).json({ message: 'Cannot mark own message as seen' });
      return;
    }

    // Verify user is member of the chat
    const chat = await Chat.findOne({
      _id: message.chatId,
      members: req.user._id,
      isActive: true,
    });

    if (!chat) {
      throw new ApiError(404, 'Chat not found or access denied');
    }

    await message.markAsSeen(req.user._id);

    res.status(200).json({
      message: 'Message marked as seen',
    });
  },
};
