{"name": "whatsapp-clone-frontend", "version": "1.0.0", "description": "Frontend for WhatsApp clone with React, TypeScript, and TailwindCSS", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "test": "vitest"}, "dependencies": {"@tanstack/react-query": "^5.12.2", "clsx": "^2.0.0", "date-fns": "^3.0.6", "firebase": "^10.7.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.5.3", "react-router-dom": "^6.20.1", "react-window": "^1.8.8", "react-window-infinite-loader": "^1.0.9", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.2.0", "zustand": "^4.4.7"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.80.6", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}}