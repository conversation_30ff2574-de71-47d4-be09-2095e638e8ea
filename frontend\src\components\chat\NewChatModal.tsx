import React, { useState } from 'react';
import { Search, X, Users, MessageCircle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';

import { apiClient } from '@/api/client';
import { User, ChatType } from '@/types';
import { useChats } from '@/hooks/useChats';

import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import Avatar from '@/components/ui/Avatar';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface NewChatModalProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Modal for creating new chats
 * Allows searching users and creating direct or group chats
 */
const NewChatModal: React.FC<NewChatModalProps> = ({ isOpen, onClose }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [chatType, setChatType] = useState<ChatType>(ChatType.DIRECT);
  const [groupName, setGroupName] = useState('');

  const { createChat, isCreating } = useChats();

  // Search users
  const { data: users = [], isLoading } = useQuery({
    queryKey: ['users', 'search', searchQuery],
    queryFn: async () => {
      if (!searchQuery.trim()) {
        const response = await apiClient.get('/users');
        return response.users as User[];
      } else {
        const response = await apiClient.get('/users/search', { q: searchQuery });
        return response.users as User[];
      }
    },
    enabled: isOpen,
    staleTime: 30 * 1000, // 30 seconds
  });

  // Handle user selection
  const handleUserSelect = (user: User) => {
    if (chatType === ChatType.DIRECT) {
      // For direct chats, immediately create chat
      createChat({
        type: ChatType.DIRECT,
        members: [user._id],
      });
      onClose();
    } else {
      // For group chats, add to selection
      if (!selectedUsers.find(u => u._id === user._id)) {
        setSelectedUsers([...selectedUsers, user]);
      }
    }
  };

  // Handle user removal from selection
  const handleUserRemove = (userId: string) => {
    setSelectedUsers(selectedUsers.filter(u => u._id !== userId));
  };

  // Handle create group chat
  const handleCreateGroup = () => {
    if (selectedUsers.length < 2) {
      return;
    }

    createChat({
      type: ChatType.GROUP,
      members: selectedUsers.map(u => u._id),
      name: groupName.trim() || undefined,
    });
    
    onClose();
  };

  // Reset modal state when closed
  React.useEffect(() => {
    if (!isOpen) {
      setSearchQuery('');
      setSelectedUsers([]);
      setChatType(ChatType.DIRECT);
      setGroupName('');
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[80vh] flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">New Chat</h2>
            <Button variant="ghost" size="sm" onClick={onClose} className="p-1">
              <X className="w-5 h-5" />
            </Button>
          </div>

          {/* Chat type selector */}
          <div className="flex space-x-2 mb-4">
            <Button
              variant={chatType === ChatType.DIRECT ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => setChatType(ChatType.DIRECT)}
              leftIcon={<MessageCircle className="w-4 h-4" />}
            >
              Direct
            </Button>
            <Button
              variant={chatType === ChatType.GROUP ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => setChatType(ChatType.GROUP)}
              leftIcon={<Users className="w-4 h-4" />}
            >
              Group
            </Button>
          </div>

          {/* Group name input */}
          {chatType === ChatType.GROUP && (
            <Input
              placeholder="Group name (optional)"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              className="mb-4"
            />
          )}

          {/* Search input */}
          <Input
            placeholder="Search users..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            leftIcon={<Search className="w-4 h-4" />}
          />
        </div>

        {/* Selected users (for group chats) */}
        {chatType === ChatType.GROUP && selectedUsers.length > 0 && (
          <div className="p-4 border-b border-gray-200">
            <p className="text-sm font-medium text-gray-700 mb-2">
              Selected ({selectedUsers.length})
            </p>
            <div className="flex flex-wrap gap-2">
              {selectedUsers.map(user => (
                <div
                  key={user._id}
                  className="flex items-center space-x-2 bg-whatsapp-green-light rounded-full px-3 py-1"
                >
                  <Avatar src={user.avatar} name={user.name} size="sm" />
                  <span className="text-sm">{user.name}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleUserRemove(user._id)}
                    className="p-0.5 hover:bg-red-100"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* User list */}
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <LoadingSpinner />
            </div>
          ) : users.length === 0 ? (
            <div className="flex items-center justify-center p-8">
              <p className="text-gray-500">
                {searchQuery ? 'No users found' : 'No users available'}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-100">
              {users.map(user => {
                const isSelected = selectedUsers.find(u => u._id === user._id);
                
                return (
                  <div
                    key={user._id}
                    className={`flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors ${
                      isSelected ? 'bg-whatsapp-green-light' : ''
                    }`}
                    onClick={() => handleUserSelect(user)}
                  >
                    <Avatar
                      src={user.avatar}
                      name={user.name}
                      size="md"
                      isOnline={user.isOnline}
                      className="flex-shrink-0"
                    />
                    <div className="flex-1 min-w-0 ml-3">
                      <h3 className="text-sm font-medium text-gray-900 truncate">
                        {user.name}
                      </h3>
                      <p className="text-sm text-gray-500 truncate">{user.email}</p>
                    </div>
                    {isSelected && (
                      <div className="w-5 h-5 bg-whatsapp-green rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Footer (for group chats) */}
        {chatType === ChatType.GROUP && (
          <div className="p-4 border-t border-gray-200">
            <Button
              variant="primary"
              onClick={handleCreateGroup}
              disabled={selectedUsers.length < 2 || isCreating}
              isLoading={isCreating}
              className="w-full"
            >
              Create Group ({selectedUsers.length})
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default NewChatModal;
