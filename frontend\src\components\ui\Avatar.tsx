import React from 'react';
import { clsx } from 'clsx';

interface AvatarProps {
  src?: string;
  alt?: string;
  name?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  isOnline?: boolean;
  className?: string;
}

/**
 * Avatar component with online status indicator
 * Shows user image or initials fallback
 */
const Avatar: React.FC<AvatarProps> = ({
  src,
  alt,
  name = '',
  size = 'md',
  isOnline,
  className,
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base',
    xl: 'w-16 h-16 text-lg',
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={clsx('relative inline-block', className)}>
      <div
        className={clsx(
          'rounded-full object-cover bg-gray-200 flex items-center justify-center text-gray-600 font-medium overflow-hidden',
          sizeClasses[size]
        )}
      >
        {src ? (
          <img
            src={src}
            alt={alt || name}
            className="w-full h-full object-cover"
            onError={(e) => {
              // Hide image on error and show initials
              e.currentTarget.style.display = 'none';
            }}
          />
        ) : (
          <span>{getInitials(name)}</span>
        )}
        
        {/* Fallback initials if image fails to load */}
        {src && (
          <span className="absolute inset-0 flex items-center justify-center text-gray-600 font-medium">
            {getInitials(name)}
          </span>
        )}
      </div>

      {/* Online status indicator */}
      {isOnline !== undefined && (
        <div
          className={clsx(
            'absolute bottom-0 right-0 rounded-full border-2 border-white',
            isOnline ? 'bg-green-500' : 'bg-gray-400',
            size === 'sm' ? 'w-2.5 h-2.5' : 'w-3 h-3'
          )}
        />
      )}
    </div>
  );
};

export default Avatar;
