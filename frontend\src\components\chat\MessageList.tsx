import React, { useEffect, useRef } from 'react';
import { useInfiniteQuery } from '@tanstack/react-query';

import { useChatStore } from '@/store/chatStore';
import { useChatSocket } from '@/hooks/useChatSocket';
import { apiClient } from '@/api/client';
import { Message } from '@/types';

import LoadingSpinner from '@/components/ui/LoadingSpinner';
import MessageItem from './MessageItem';

interface MessageListProps {
  chatId: string;
  currentUserId: string;
}

/**
 * Message list component with infinite scroll
 * Displays messages in a virtualized list
 */
const MessageList: React.FC<MessageListProps> = ({ chatId, currentUserId }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const { messages, setMessages, prependMessages } = useChatStore();
  const { markAsSeen } = useChatSocket();

  const chatMessages = messages[chatId] || [];

  // Fetch messages with infinite scroll
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
  } = useInfiniteQuery({
    queryKey: ['messages', chatId],
    queryFn: async ({ pageParam }) => {
      const response = await apiClient.get(`/messages/${chatId}`, {
        limit: 50,
        cursor: pageParam,
      });
      return response;
    },
    getNextPageParam: (lastPage) => {
      return lastPage.hasMore ? lastPage.nextCursor : undefined;
    },
    enabled: !!chatId,
    onSuccess: (data) => {
      // Flatten all pages and set messages
      const allMessages = data.pages.flatMap(page => page.messages);
      setMessages(chatId, allMessages);
    },
  });

  // Scroll to bottom when new messages arrive
  const scrollToBottom = (smooth = true) => {
    messagesEndRef.current?.scrollIntoView({ 
      behavior: smooth ? 'smooth' : 'auto' 
    });
  };

  // Load more messages when scrolling to top
  const handleScroll = () => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const { scrollTop } = container;
    
    // Load more messages when near the top
    if (scrollTop < 100 && hasNextPage && !isFetchingNextPage) {
      const previousScrollHeight = container.scrollHeight;
      
      fetchNextPage().then(() => {
        // Maintain scroll position after loading more messages
        requestAnimationFrame(() => {
          const newScrollHeight = container.scrollHeight;
          container.scrollTop = newScrollHeight - previousScrollHeight;
        });
      });
    }
  };

  // Mark messages as seen when they come into view
  const markMessagesAsSeen = () => {
    const unseenMessages = chatMessages.filter(
      message => 
        message.senderId._id !== currentUserId &&
        !message.status.seen.includes(currentUserId)
    );

    unseenMessages.forEach(message => {
      markAsSeen(message._id);
    });
  };

  // Scroll to bottom on initial load and new messages
  useEffect(() => {
    if (chatMessages.length > 0) {
      scrollToBottom(false);
      markMessagesAsSeen();
    }
  }, [chatId]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const isNearBottom = 
      container.scrollHeight - container.scrollTop - container.clientHeight < 100;

    if (isNearBottom) {
      scrollToBottom();
      markMessagesAsSeen();
    }
  }, [chatMessages.length]);

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-2 text-gray-500">Loading messages...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-2">Failed to load messages</p>
          <button
            onClick={() => window.location.reload()}
            className="text-whatsapp-green hover:underline"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={messagesContainerRef}
      className="flex-1 overflow-y-auto p-4 space-y-2"
      onScroll={handleScroll}
      style={{
        backgroundImage: `url("data:image/svg+xml,%3csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23f0f0f0' fill-opacity='0.1'%3e%3cpath d='m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e")`,
      }}
    >
      {/* Loading more indicator */}
      {isFetchingNextPage && (
        <div className="flex justify-center py-4">
          <LoadingSpinner size="sm" />
        </div>
      )}

      {/* Messages */}
      {chatMessages.length === 0 ? (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <p className="text-gray-500 mb-2">No messages yet</p>
            <p className="text-sm text-gray-400">
              Start the conversation by sending a message
            </p>
          </div>
        </div>
      ) : (
        <>
          {chatMessages.map((message, index) => {
            const previousMessage = index > 0 ? chatMessages[index - 1] : null;
            const showSenderInfo = 
              !previousMessage || 
              previousMessage.senderId._id !== message.senderId._id ||
              new Date(message.timestamp).getTime() - new Date(previousMessage.timestamp).getTime() > 5 * 60 * 1000; // 5 minutes

            return (
              <MessageItem
                key={message._id}
                message={message}
                isOwn={message.senderId._id === currentUserId}
                showSenderInfo={showSenderInfo}
              />
            );
          })}
        </>
      )}

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
