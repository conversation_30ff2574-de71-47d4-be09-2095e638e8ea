import { useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

import { apiClient } from '@/api/client';
import { Message, SendMessageRequest, EditMessageRequest } from '@/types';
import { useChatStore } from '@/store/chatStore';

/**
 * Custom hook for message-related operations
 */
export const useMessages = (chatId: string) => {
  const queryClient = useQueryClient();
  const { setMessages, addMessage, updateMessage, removeMessage } = useChatStore();

  // Fetch messages with infinite scroll
  const messagesQuery = useInfiniteQuery({
    queryKey: ['messages', chatId],
    queryFn: async ({ pageParam }) => {
      const response = await apiClient.get(`/messages/${chatId}`, {
        limit: 50,
        cursor: pageParam,
      });
      return response;
    },
    getNextPageParam: (lastPage) => {
      return lastPage.hasMore ? lastPage.nextCursor : undefined;
    },
    enabled: !!chatId,
    onSuccess: (data) => {
      // Flatten all pages and set messages
      const allMessages = data.pages.flatMap(page => page.messages);
      setMessages(chatId, allMessages);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Send message mutation
  const sendMessageMutation = useMutation({
    mutationFn: async (data: SendMessageRequest) => {
      const response = await apiClient.post('/messages', data);
      return response.data as Message;
    },
    onSuccess: (newMessage) => {
      // Add to store (optimistic update is handled by socket)
      addMessage(chatId, newMessage);
      
      // Update chats query to reflect new last message
      queryClient.invalidateQueries({ queryKey: ['chats'] });
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to send message');
    },
  });

  // Edit message mutation
  const editMessageMutation = useMutation({
    mutationFn: async ({ messageId, data }: { messageId: string; data: EditMessageRequest }) => {
      const response = await apiClient.put(`/messages/${messageId}`, data);
      return response.data as Message;
    },
    onSuccess: (updatedMessage) => {
      // Update in store
      updateMessage(chatId, updatedMessage._id, updatedMessage);
      
      toast.success('Message updated');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to edit message');
    },
  });

  // Delete message mutation
  const deleteMessageMutation = useMutation({
    mutationFn: async (messageId: string) => {
      await apiClient.delete(`/messages/${messageId}`);
      return messageId;
    },
    onSuccess: (messageId) => {
      // Remove from store
      removeMessage(chatId, messageId);
      
      toast.success('Message deleted');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete message');
    },
  });

  // Mark as delivered mutation
  const markAsDeliveredMutation = useMutation({
    mutationFn: async (messageId: string) => {
      await apiClient.post(`/messages/${messageId}/delivered`);
    },
    onError: (error: any) => {
      console.error('Failed to mark message as delivered:', error);
    },
  });

  // Mark as seen mutation
  const markAsSeenMutation = useMutation({
    mutationFn: async (messageId: string) => {
      await apiClient.post(`/messages/${messageId}/seen`);
    },
    onError: (error: any) => {
      console.error('Failed to mark message as seen:', error);
    },
  });

  return {
    // Query data
    messages: messagesQuery.data?.pages.flatMap(page => page.messages) || [],
    hasNextPage: messagesQuery.hasNextPage,
    isLoading: messagesQuery.isLoading,
    isFetchingNextPage: messagesQuery.isFetchingNextPage,
    error: messagesQuery.error,
    fetchNextPage: messagesQuery.fetchNextPage,
    refetch: messagesQuery.refetch,

    // Mutations
    sendMessage: sendMessageMutation.mutate,
    editMessage: editMessageMutation.mutate,
    deleteMessage: deleteMessageMutation.mutate,
    markAsDelivered: markAsDeliveredMutation.mutate,
    markAsSeen: markAsSeenMutation.mutate,

    // Loading states
    isSending: sendMessageMutation.isPending,
    isEditing: editMessageMutation.isPending,
    isDeleting: deleteMessageMutation.isPending,
  };
};
