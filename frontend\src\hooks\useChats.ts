import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';

import { apiClient } from '@/api/client';
import { Chat, CreateChatRequest, UpdateChatRequest } from '@/types';
import { useChatStore } from '@/store/chatStore';

/**
 * Custom hook for chat-related operations
 */
export const useChats = () => {
  const queryClient = useQueryClient();
  const { setChats } = useChatStore();

  // Fetch user's chats
  const chatsQuery = useQuery({
    queryKey: ['chats'],
    queryFn: async () => {
      const response = await apiClient.get('/chats');
      return response.chats as Chat[];
    },
    onSuccess: (data) => {
      setChats(data);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Create new chat mutation
  const createChatMutation = useMutation({
    mutationFn: async (data: CreateChatRequest) => {
      const response = await apiClient.post('/chats', data);
      return response.chat as Chat;
    },
    onSuccess: (newChat) => {
      // Add to query cache
      queryClient.setQueryData(['chats'], (oldChats: Chat[] = []) => [
        newChat,
        ...oldChats.filter(chat => chat._id !== newChat._id)
      ]);
      
      // Update store
      const currentChats = useChatStore.getState().chats;
      useChatStore.getState().setChats([
        newChat,
        ...currentChats.filter(chat => chat._id !== newChat._id)
      ]);
      
      toast.success('Chat created successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create chat');
    },
  });

  // Update chat mutation
  const updateChatMutation = useMutation({
    mutationFn: async ({ chatId, data }: { chatId: string; data: UpdateChatRequest }) => {
      const response = await apiClient.put(`/chats/${chatId}`, data);
      return response.chat as Chat;
    },
    onSuccess: (updatedChat) => {
      // Update query cache
      queryClient.setQueryData(['chats'], (oldChats: Chat[] = []) =>
        oldChats.map(chat => chat._id === updatedChat._id ? updatedChat : chat)
      );
      
      // Update store
      useChatStore.getState().updateChat(updatedChat._id, updatedChat);
      
      toast.success('Chat updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update chat');
    },
  });

  // Delete/leave chat mutation
  const deleteChatMutation = useMutation({
    mutationFn: async (chatId: string) => {
      await apiClient.delete(`/chats/${chatId}`);
      return chatId;
    },
    onSuccess: (chatId) => {
      // Remove from query cache
      queryClient.setQueryData(['chats'], (oldChats: Chat[] = []) =>
        oldChats.filter(chat => chat._id !== chatId)
      );
      
      // Update store
      useChatStore.getState().removeChat(chatId);
      
      toast.success('Left chat successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to leave chat');
    },
  });

  // Add member mutation
  const addMemberMutation = useMutation({
    mutationFn: async ({ chatId, userId }: { chatId: string; userId: string }) => {
      const response = await apiClient.post(`/chats/${chatId}/members`, { userId });
      return response.chat as Chat;
    },
    onSuccess: (updatedChat) => {
      // Update query cache
      queryClient.setQueryData(['chats'], (oldChats: Chat[] = []) =>
        oldChats.map(chat => chat._id === updatedChat._id ? updatedChat : chat)
      );
      
      // Update store
      useChatStore.getState().updateChat(updatedChat._id, updatedChat);
      
      toast.success('Member added successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to add member');
    },
  });

  // Remove member mutation
  const removeMemberMutation = useMutation({
    mutationFn: async ({ chatId, userId }: { chatId: string; userId: string }) => {
      const response = await apiClient.delete(`/chats/${chatId}/members/${userId}`);
      return response.chat as Chat;
    },
    onSuccess: (updatedChat) => {
      // Update query cache
      queryClient.setQueryData(['chats'], (oldChats: Chat[] = []) =>
        oldChats.map(chat => chat._id === updatedChat._id ? updatedChat : chat)
      );
      
      // Update store
      useChatStore.getState().updateChat(updatedChat._id, updatedChat);
      
      toast.success('Member removed successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to remove member');
    },
  });

  return {
    // Queries
    chats: chatsQuery.data || [],
    isLoading: chatsQuery.isLoading,
    error: chatsQuery.error,
    refetch: chatsQuery.refetch,

    // Mutations
    createChat: createChatMutation.mutate,
    updateChat: updateChatMutation.mutate,
    deleteChat: deleteChatMutation.mutate,
    addMember: addMemberMutation.mutate,
    removeMember: removeMemberMutation.mutate,

    // Loading states
    isCreating: createChatMutation.isPending,
    isUpdating: updateChatMutation.isPending,
    isDeleting: deleteChatMutation.isPending,
    isAddingMember: addMemberMutation.isPending,
    isRemovingMember: removeMemberMutation.isPending,
  };
};
