import React from 'react';
import { ArrowLeft, Phone, Video, MoreVertical } from 'lucide-react';

import { useChatStore } from '@/store/chatStore';
import { useAuthStore } from '@/store/authStore';
import { ChatType } from '@/types';

import Avatar from '@/components/ui/Avatar';
import Button from '@/components/ui/Button';
import MessageList from './MessageList';
import MessageInput from './MessageInput';

/**
 * Main chat window component
 * Shows chat header, messages, and input
 */
const ChatWindow: React.FC = () => {
  const { user } = useAuthStore();
  const { selectedChatId, selectedChat, setSelectedChat } = useChatStore();

  // Handle back button (mobile)
  const handleBack = () => {
    setSelectedChat(null);
  };

  // Handle voice call
  const handleVoiceCall = () => {
    // TODO: Implement voice call
    console.log('Voice call clicked');
  };

  // Handle video call
  const handleVideoCall = () => {
    // TODO: Implement video call
    console.log('Video call clicked');
  };

  // Handle menu
  const handleMenu = () => {
    // TODO: Implement chat menu
    console.log('Menu clicked');
  };

  // Show welcome screen if no chat selected
  if (!selectedChatId || !selectedChat) {
    return (
      <div className="flex-1 bg-gray-50 flex flex-col">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md px-4">
            <div className="w-16 h-16 bg-whatsapp-green rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Welcome to WhatsApp Clone
            </h3>
            <p className="text-gray-600">
              Select a chat to start messaging, or create a new chat to connect with friends and family.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex-1 bg-gray-50 flex items-center justify-center">
        <p className="text-gray-500">Loading...</p>
      </div>
    );
  }

  // Get chat display info
  const getChatDisplayInfo = () => {
    if (selectedChat.type === ChatType.GROUP) {
      return {
        name: selectedChat.name || 'Group Chat',
        subtitle: `${selectedChat.members.length} members`,
        avatar: selectedChat.avatar,
        isOnline: false,
      };
    } else {
      // Direct chat - find the other user
      const otherUser = selectedChat.members.find(member => member._id !== user._id);
      return {
        name: otherUser?.name || 'Unknown User',
        subtitle: otherUser?.isOnline ? 'Online' : 'Offline',
        avatar: otherUser?.avatar,
        isOnline: otherUser?.isOnline || false,
      };
    }
  };

  const { name, subtitle, avatar, isOnline } = getChatDisplayInfo();

  return (
    <div className="flex-1 bg-gray-50 flex flex-col h-full">
      {/* Chat Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {/* Back button (mobile only) */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="md:hidden p-2"
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>

            {/* Chat info */}
            <Avatar
              src={avatar}
              name={name}
              size="md"
              isOnline={selectedChat.type === ChatType.DIRECT ? isOnline : undefined}
            />
            
            <div className="flex-1 min-w-0">
              <h2 className="text-lg font-semibold text-gray-900 truncate">
                {name}
              </h2>
              <p className="text-sm text-gray-500 truncate">{subtitle}</p>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleVoiceCall}
              className="p-2"
              title="Voice call"
            >
              <Phone className="w-5 h-5" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleVideoCall}
              className="p-2"
              title="Video call"
            >
              <Video className="w-5 h-5" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleMenu}
              className="p-2"
              title="Menu"
            >
              <MoreVertical className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-hidden">
        <MessageList chatId={selectedChatId} currentUserId={user._id} />
      </div>

      {/* Message Input */}
      <div className="bg-white border-t border-gray-200">
        <MessageInput chatId={selectedChatId} />
      </div>
    </div>
  );
};

export default ChatWindow;
