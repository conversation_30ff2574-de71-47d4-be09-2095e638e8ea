import React, { useState, useRef, useEffect } from 'react';
import { Send, Paperclip, Smile } from 'lucide-react';

import { useChatSocket } from '@/hooks/useChatSocket';
import Button from '@/components/ui/Button';

interface MessageInputProps {
  chatId: string;
  replyTo?: any;
  onCancelReply?: () => void;
}

/**
 * Message input component
 * Handles text input, sending messages, and typing indicators
 */
const MessageInput: React.FC<MessageInputProps> = ({
  chatId,
  replyTo,
  onCancelReply,
}) => {
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();
  
  const { sendMessage, startTyping, stopTyping } = useChatSocket();

  // Auto-resize textarea
  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);
    adjustTextareaHeight();

    // Handle typing indicators
    if (value.trim() && !isTyping) {
      setIsTyping(true);
      startTyping(chatId);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        stopTyping(chatId);
      }
    }, 1000);
  };

  // Handle send message
  const handleSendMessage = () => {
    const trimmedMessage = message.trim();
    if (!trimmedMessage) return;

    // Send message via socket
    sendMessage({
      chatId,
      text: trimmedMessage,
      replyTo: replyTo?._id,
    });

    // Clear input and stop typing
    setMessage('');
    setIsTyping(false);
    stopTyping(chatId);
    
    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Reset textarea height
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }

    // Cancel reply if exists
    if (replyTo && onCancelReply) {
      onCancelReply();
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Handle attachment
  const handleAttachment = () => {
    // TODO: Implement file attachment
    console.log('Attachment clicked');
  };

  // Handle emoji
  const handleEmoji = () => {
    // TODO: Implement emoji picker
    console.log('Emoji clicked');
  };

  // Cleanup typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      if (isTyping) {
        stopTyping(chatId);
      }
    };
  }, [chatId, isTyping, stopTyping]);

  // Focus textarea when chat changes
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [chatId]);

  return (
    <div className="p-4">
      {/* Reply preview */}
      {replyTo && (
        <div className="mb-3 p-3 bg-gray-50 border-l-4 border-whatsapp-green rounded">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-xs font-medium text-whatsapp-green">
                Replying to {replyTo.senderId.name}
              </p>
              <p className="text-sm text-gray-700 truncate">{replyTo.text}</p>
            </div>
            {onCancelReply && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onCancelReply}
                className="p-1 ml-2"
              >
                ×
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Input area */}
      <div className="flex items-end space-x-2">
        {/* Attachment button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleAttachment}
          className="p-2 flex-shrink-0"
          title="Attach file"
        >
          <Paperclip className="w-5 h-5" />
        </Button>

        {/* Text input */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="Type a message..."
            className="w-full px-4 py-2 pr-12 border border-gray-300 rounded-full resize-none focus:outline-none focus:ring-2 focus:ring-whatsapp-green focus:border-transparent transition-colors duration-200"
            rows={1}
            style={{ minHeight: '40px', maxHeight: '120px' }}
          />

          {/* Emoji button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEmoji}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1"
            title="Add emoji"
          >
            <Smile className="w-5 h-5" />
          </Button>
        </div>

        {/* Send button */}
        <Button
          variant="primary"
          size="sm"
          onClick={handleSendMessage}
          disabled={!message.trim()}
          className="p-2 flex-shrink-0 rounded-full"
          title="Send message"
        >
          <Send className="w-5 h-5" />
        </Button>
      </div>
    </div>
  );
};

export default MessageInput;
