import React from 'react';
import { formatDistanceToNow, format } from 'date-fns';
import { clsx } from 'clsx';
import { Check, CheckCheck } from 'lucide-react';

import { Message } from '@/types';
import Avatar from '@/components/ui/Avatar';

interface MessageItemProps {
  message: Message;
  isOwn: boolean;
  showSenderInfo: boolean;
}

/**
 * Individual message item component
 * Shows message content, timestamp, and status
 */
const MessageItem: React.FC<MessageItemProps> = ({
  message,
  isOwn,
  showSenderInfo,
}) => {
  // Format timestamp
  const getTimeDisplay = () => {
    const messageDate = new Date(message.timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return format(messageDate, 'HH:mm');
    } else if (diffInHours < 24 * 7) {
      return format(messageDate, 'EEE HH:mm');
    } else {
      return format(messageDate, 'MMM dd, HH:mm');
    }
  };

  // Get message status icon (only for own messages)
  const getStatusIcon = () => {
    if (!isOwn) return null;

    const hasSeenStatus = message.status.seen.length > 0;
    const hasDeliveredStatus = message.status.delivered.length > 0;

    if (hasSeenStatus) {
      return <CheckCheck className="w-4 h-4 text-whatsapp-blue" />;
    } else if (hasDeliveredStatus) {
      return <CheckCheck className="w-4 h-4 text-gray-400" />;
    } else {
      return <Check className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div
      className={clsx(
        'flex items-end space-x-2 max-w-xs md:max-w-md',
        isOwn ? 'ml-auto flex-row-reverse space-x-reverse' : 'mr-auto'
      )}
    >
      {/* Avatar (only for received messages and when showing sender info) */}
      {!isOwn && showSenderInfo && (
        <Avatar
          src={message.senderId.avatar}
          name={message.senderId.name}
          size="sm"
          className="flex-shrink-0"
        />
      )}

      {/* Spacer when not showing avatar */}
      {!isOwn && !showSenderInfo && <div className="w-8" />}

      {/* Message bubble */}
      <div
        className={clsx(
          'rounded-lg px-3 py-2 max-w-full break-words',
          isOwn
            ? 'bg-whatsapp-green-light text-gray-800'
            : 'bg-white text-gray-800 shadow-sm'
        )}
      >
        {/* Sender name (only for received messages in groups when showing sender info) */}
        {!isOwn && showSenderInfo && (
          <p className="text-xs font-medium text-whatsapp-green mb-1">
            {message.senderId.name}
          </p>
        )}

        {/* Reply to message */}
        {message.replyTo && (
          <div className="mb-2 p-2 border-l-4 border-whatsapp-green bg-gray-50 rounded">
            <p className="text-xs text-gray-600 font-medium">
              {message.replyTo.senderId.name}
            </p>
            <p className="text-sm text-gray-700 truncate">
              {message.replyTo.text}
            </p>
          </div>
        )}

        {/* Message text */}
        <p className="text-sm whitespace-pre-wrap">{message.text}</p>

        {/* Edited indicator */}
        {message.isEdited && (
          <span className="text-xs text-gray-500 italic ml-2">edited</span>
        )}

        {/* Timestamp and status */}
        <div
          className={clsx(
            'flex items-center justify-end mt-1 space-x-1',
            isOwn ? 'text-gray-600' : 'text-gray-500'
          )}
        >
          <span className="text-xs">{getTimeDisplay()}</span>
          {getStatusIcon()}
        </div>
      </div>
    </div>
  );
};

export default MessageItem;
