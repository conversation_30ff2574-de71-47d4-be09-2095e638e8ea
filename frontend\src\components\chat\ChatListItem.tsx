import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { clsx } from 'clsx';
import { Check, CheckCheck } from 'lucide-react';

import { Chat, ChatType } from '@/types';
import { useChatStore } from '@/store/chatStore';
import Avatar from '@/components/ui/Avatar';

interface ChatListItemProps {
  chat: Chat;
  isSelected: boolean;
  onClick: () => void;
  currentUserId: string;
}

/**
 * Individual chat list item component
 * Shows chat info, last message, and status
 */
const ChatListItem: React.FC<ChatListItemProps> = ({
  chat,
  isSelected,
  onClick,
  currentUserId,
}) => {
  const { typingUsers } = useChatStore();
  const isTyping = typingUsers[chat._id]?.length > 0;

  // Get chat display info
  const getChatDisplayInfo = () => {
    if (chat.type === ChatType.GROUP) {
      return {
        name: chat.name || 'Group Chat',
        avatar: chat.avatar,
        isOnline: false, // Groups don't have online status
      };
    } else {
      // Direct chat - find the other user
      const otherUser = chat.members.find(member => member._id !== currentUserId);
      return {
        name: otherUser?.name || 'Unknown User',
        avatar: otherUser?.avatar,
        isOnline: otherUser?.isOnline || false,
      };
    }
  };

  const { name, avatar, isOnline } = getChatDisplayInfo();

  // Format last message
  const getLastMessageDisplay = () => {
    if (isTyping) {
      const typingNames = typingUsers[chat._id];
      if (typingNames.length === 1) {
        return `${typingNames[0]} is typing...`;
      } else if (typingNames.length === 2) {
        return `${typingNames[0]} and ${typingNames[1]} are typing...`;
      } else {
        return `${typingNames.length} people are typing...`;
      }
    }

    if (!chat.lastMessage) {
      return 'No messages yet';
    }

    const message = chat.lastMessage;
    const isOwnMessage = message.senderId._id === currentUserId;
    const prefix = isOwnMessage ? 'You: ' : '';
    
    return `${prefix}${message.text}`;
  };

  // Get message status icon for own messages
  const getMessageStatusIcon = () => {
    if (!chat.lastMessage || chat.lastMessage.senderId._id !== currentUserId) {
      return null;
    }

    const message = chat.lastMessage;
    const hasSeenStatus = message.status.seen.length > 0;
    const hasDeliveredStatus = message.status.delivered.length > 0;

    if (hasSeenStatus) {
      return <CheckCheck className="w-4 h-4 text-whatsapp-blue" />;
    } else if (hasDeliveredStatus) {
      return <CheckCheck className="w-4 h-4 text-gray-400" />;
    } else {
      return <Check className="w-4 h-4 text-gray-400" />;
    }
  };

  // Format timestamp
  const getTimeDisplay = () => {
    if (!chat.lastActivity) return '';
    
    try {
      return formatDistanceToNow(new Date(chat.lastActivity), { addSuffix: false });
    } catch {
      return '';
    }
  };

  return (
    <div
      className={clsx(
        'flex items-center p-3 hover:bg-gray-50 cursor-pointer transition-colors duration-150',
        isSelected && 'bg-whatsapp-gray-medium'
      )}
      onClick={onClick}
    >
      {/* Avatar */}
      <Avatar
        src={avatar}
        name={name}
        size="lg"
        isOnline={chat.type === ChatType.DIRECT ? isOnline : undefined}
        className="flex-shrink-0"
      />

      {/* Chat Info */}
      <div className="flex-1 min-w-0 ml-3">
        <div className="flex items-center justify-between mb-1">
          <h3 className="text-sm font-medium text-gray-900 truncate">
            {name}
          </h3>
          <span className="text-xs text-gray-500 flex-shrink-0 ml-2">
            {getTimeDisplay()}
          </span>
        </div>

        <div className="flex items-center justify-between">
          <p
            className={clsx(
              'text-sm truncate',
              isTyping ? 'text-whatsapp-green italic' : 'text-gray-600'
            )}
          >
            {getLastMessageDisplay()}
          </p>

          {/* Message status icon */}
          <div className="flex-shrink-0 ml-2">
            {getMessageStatusIcon()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatListItem;
