import React from 'react';
import { useParams } from 'react-router-dom';

import { useChatStore } from '@/store/chatStore';
import { useAuthStore } from '@/store/authStore';

// Import real components
import ChatSidebar from '@/components/chat/ChatSidebar';
import ChatWindow from '@/components/chat/ChatWindow';

/**
 * Main chat layout component
 * Displays sidebar with chat list and main chat window
 */
const ChatLayout: React.FC = () => {
  const { chatId } = useParams<{ chatId: string }>();
  const { user } = useAuthStore();
  const { selectedChatId, setSelectedChat } = useChatStore();

  // Set selected chat from URL parameter
  React.useEffect(() => {
    if (chatId && chatId !== selectedChatId) {
      setSelectedChat(chatId);
    }
  }, [chatId, selectedChatId, setSelectedChat]);

  if (!user) {
    return (
      <div className="min-h-screen bg-whatsapp-gray-light flex items-center justify-center">
        <p className="text-whatsapp-gray-dark">Loading...</p>
      </div>
    );
  }

  return (
    <div className="h-screen bg-whatsapp-gray-light flex">
      {/* Mobile: Show sidebar or chat window based on selection */}
      <div className="md:hidden w-full">
        {selectedChatId ? <ChatWindow /> : <ChatSidebar />}
      </div>

      {/* Desktop: Show both sidebar and chat window */}
      <div className="hidden md:flex w-full">
        <ChatSidebar />
        <ChatWindow />
      </div>
    </div>
  );
};

export default ChatLayout;
