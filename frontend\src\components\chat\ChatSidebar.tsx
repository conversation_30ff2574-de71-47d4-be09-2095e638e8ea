import React, { useState } from 'react';
import { Search, Plus, MoreVertical, MessageCircle } from 'lucide-react';

import { useAuthStore } from '@/store/authStore';
import { useChatStore } from '@/store/chatStore';
import { useChats } from '@/hooks/useChats';

import Avatar from '@/components/ui/Avatar';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import LoadingSpinner from '@/components/ui/LoadingSpinner';
import ChatListItem from './ChatListItem';
import NewChatModal from './NewChatModal';

/**
 * Chat sidebar component
 * Shows user profile, search, and chat list
 */
const ChatSidebar: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewChatModal, setShowNewChatModal] = useState(false);
  const { user } = useAuthStore();
  const { chats, selectedChatId, setSelectedChat } = useChatStore();

  // Use the custom hook for chats
  const { isLoading, error } = useChats();

  // Filter chats based on search query
  const filteredChats = chats.filter(chat => {
    if (!searchQuery.trim()) return true;
    
    const query = searchQuery.toLowerCase();
    
    // Search by chat name (for groups)
    if (chat.name && chat.name.toLowerCase().includes(query)) {
      return true;
    }
    
    // Search by member names
    return chat.members.some(member => 
      member.name.toLowerCase().includes(query) ||
      member.email.toLowerCase().includes(query)
    );
  });

  const handleChatSelect = (chatId: string) => {
    setSelectedChat(chatId);
  };

  const handleNewChat = () => {
    setShowNewChatModal(true);
  };

  if (!user) {
    return (
      <div className="w-full md:w-1/3 lg:w-1/4 bg-white border-r border-gray-200 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="w-full md:w-1/3 lg:w-1/4 bg-white border-r border-gray-200 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-whatsapp-gray-light">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Avatar
              src={user.avatar}
              name={user.name}
              size="md"
              isOnline={user.isOnline}
            />
            <div className="flex-1 min-w-0">
              <h2 className="text-lg font-semibold text-gray-900 truncate">
                {user.name}
              </h2>
              <p className="text-sm text-gray-500 truncate">{user.email}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNewChat}
              className="p-2"
              title="New chat"
            >
              <Plus className="w-5 h-5" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="p-2"
              title="Menu"
            >
              <MoreVertical className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Search */}
        <Input
          type="text"
          placeholder="Search chats..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          leftIcon={<Search className="w-4 h-4" />}
          className="bg-white"
        />
      </div>

      {/* Chat List */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <LoadingSpinner size="lg" />
              <p className="mt-2 text-gray-500">Loading chats...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Failed to load chats</p>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.location.reload()}
                className="mt-2"
              >
                Retry
              </Button>
            </div>
          </div>
        ) : filteredChats.length === 0 ? (
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500 mb-2">
                {searchQuery ? 'No chats found' : 'No chats yet'}
              </p>
              {!searchQuery && (
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleNewChat}
                  leftIcon={<Plus className="w-4 h-4" />}
                >
                  Start a chat
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {filteredChats.map((chat) => (
              <ChatListItem
                key={chat._id}
                chat={chat}
                isSelected={chat._id === selectedChatId}
                onClick={() => handleChatSelect(chat._id)}
                currentUserId={user._id}
              />
            ))}
          </div>
        )}
      </div>

      {/* New Chat Modal */}
      <NewChatModal
        isOpen={showNewChatModal}
        onClose={() => setShowNewChatModal(false)}
      />
    </div>
  );
};

export default ChatSidebar;
